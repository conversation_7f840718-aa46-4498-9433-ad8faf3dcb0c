// import React, { useState, useEffect, useCallback, useMemo } from "react";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
// import { Target, Plus, Save } from "lucide-react";
// import {
//   useMilestonesByProjectId,
//   useCreateMilestone,
//   useUpdateMilestone,
//   useDeleteMilestone,
// } from "@/hooks/queries/milestone";
// import {
//   useTasksByMilestoneId,
//   useCreateTask,
//   useUpdateTask,
//   useDeleteTask,
// } from "@/hooks/queries/task";
// import { useAppraisalCouncilByProjectId } from "@/hooks/queries/appraisal-council";
// import { Milestone } from "@/types/milestone";
// import { ProjectTask } from "@/types/task";
// import { toast } from "sonner";
// import MilestoneCard from "./MilestoneCard";

// // Helper function to format date-time for input
// const formatDateTimeLocal = (dateString: string): string => {
//   const date = new Date(dateString);
//   const year = date.getFullYear();
//   const month = String(date.getMonth() + 1).padStart(2, "0");
//   const day = String(date.getDate()).padStart(2, "0");
//   const hours = String(date.getHours()).padStart(2, "0");
//   const minutes = String(date.getMinutes()).padStart(2, "0");
//   return `${year}-${month}-${day}T${hours}:${minutes}`;
// };

// interface MeetingTaskModalProps {
//   isOpen: boolean;
//   onClose: () => void;
//   projectId: string;
// }

// interface Task {
//   id: string;
//   title: string;
//   description: string;
//   priority: string;
//   "start-date": string;
//   "end-date": string;
//   "meeting-url": string | null;
//   note: string;
//   status: string;
//   "milestone-id": string;
// }

// const MeetingTaskModal: React.FC<MeetingTaskModalProps> = ({
//   isOpen,
//   onClose,
//   projectId,
// }) => {
//   const [expandedMilestones, setExpandedMilestones] = useState<Set<string>>(
//     new Set()
//   );
//   const [creatingMilestone, setCreatingMilestone] = useState(false);
//   const [editingMilestone, setEditingMilestone] = useState<Milestone | null>(
//     null
//   );
//   const [creatingTask, setCreatingTask] = useState<string | null>(null);
//   const [editingTask, setEditingTask] = useState<{
//     task: Task | null;
//     milestoneId: string;
//   } | null>(null);
//   const [tasksByMilestone, setTasksByMilestone] = useState<
//     Record<string, ProjectTask[]>
//   >({});

//   // API hooks
//   const { data: milestonesData } = useMilestonesByProjectId(projectId);
//   const { data: appraisalCouncil } = useAppraisalCouncilByProjectId(projectId);
//   const deleteMilestoneMutation = useDeleteMilestone();
//   const deleteTaskMutation = useDeleteTask();

//   const milestones = useMemo(() => {
//     return milestonesData?.data || [];
//   }, [milestonesData]);

//   const milestoneIds = useMemo(() => {
//     return milestones.map((milestone: Milestone) => milestone.id);
//   }, [milestones]);

//   // Component for fetching tasks for each milestone
//   const MilestoneTaskFetcher: React.FC<{
//     milestoneId: string;
//     onTasksLoaded: (milestoneId: string, tasks: ProjectTask[]) => void;
//   }> = ({ milestoneId, onTasksLoaded }) => {
//     const { data: tasksData } = useTasksByMilestoneId(milestoneId, 1, 100);

//     useEffect(() => {
//       if (tasksData?.data?.["data-list"]) {
//         onTasksLoaded(milestoneId, tasksData.data["data-list"]);
//       }
//     }, [tasksData, milestoneId, onTasksLoaded]);

//     return null;
//   };

//   const handleTasksLoaded = useCallback(
//     (milestoneId: string, tasks: ProjectTask[]) => {
//       setTasksByMilestone((prev) => ({
//         ...prev,
//         [milestoneId]: tasks,
//       }));
//     },
//     []
//   );

//   const toggleMilestoneExpansion = (milestoneId: string) => {
//     setExpandedMilestones((prev) => {
//       const newSet = new Set(prev);
//       if (newSet.has(milestoneId)) {
//         newSet.delete(milestoneId);
//       } else {
//         newSet.add(milestoneId);
//       }
//       return newSet;
//     });
//   };

//   // Handle milestone operations
//   const handleCreateMilestone = () => {
//     setCreatingMilestone(true);
//   };

//   const handleEditMilestone = (milestone: Milestone) => {
//     setEditingMilestone(milestone);
//   };

//   const handleDeleteMilestone = async (milestone: Milestone) => {
//     try {
//       await deleteMilestoneMutation.mutateAsync({
//         id: milestone.id,
//         projectId: projectId,
//       });
//       toast.success("Milestone deleted successfully");
//     } catch (error) {
//       console.error("Error deleting milestone:", error);
//       toast.error("Failed to delete milestone");
//     }
//   };

//   // Handle task operations
//   const handleCreateTask = (milestoneId: string) => {
//     setCreatingTask(milestoneId);
//   };

//   const handleEditTask = (task: ProjectTask, milestoneId: string) => {
//     setEditingTask({
//       task: {
//         id: task.id,
//         title: task.name,
//         description: task.description,
//         priority: task.priority,
//         "start-date": task["start-date"],
//         "end-date": task["end-date"],
//         "meeting-url": task["meeting-url"],
//         note: task.note,
//         status: task.status,
//         "milestone-id": task["milestone-id"],
//       },
//       milestoneId,
//     });
//   };

//   const handleDeleteTask = async (task: ProjectTask) => {
//     try {
//       await deleteTaskMutation.mutateAsync(task.id);
//       toast.success("Task deleted successfully");
//     } catch (error) {
//       console.error("Error deleting task:", error);
//       toast.error("Failed to delete task");
//     }
//   };

//   // Handle form submissions
//   const handleMilestoneFormSave = () => {
//     setEditingMilestone(null);
//     setCreatingMilestone(false);
//   };

//   const handleTaskFormSave = () => {
//     setEditingTask(null);
//     setCreatingTask(null);
//   };

//   // Reset forms when modal closes
//   const handleClose = () => {
//     setEditingMilestone(null);
//     setCreatingMilestone(false);
//     setEditingTask(null);
//     setCreatingTask(null);
//     onClose();
//   };

//   // Check if any form is open
//   const isFormOpen =
//     creatingMilestone || editingMilestone || creatingTask || editingTask;

//   return (
//     <Dialog open={isOpen} onOpenChange={handleClose}>
//       <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
//         <DialogHeader className="flex-shrink-0">
//           <div className="flex items-center justify-between">
//             <div className="flex items-center gap-3">
//               <Target className="h-6 w-6 text-blue-600" />
//               <div>
//                 <DialogTitle className="text-xl font-bold">
//                   Meeting Tasks Management
//                 </DialogTitle>
//                 <p className="text-sm text-gray-600 mt-1">
//                   {appraisalCouncil
//                     ? `Council: ${appraisalCouncil.name}`
//                     : "No council assigned"}
//                 </p>
//               </div>
//             </div>
//             <div className="flex items-center gap-2">
//               {!isFormOpen && (
//                 <Button onClick={handleCreateMilestone} size="sm">
//                   <Plus className="w-4 h-4 mr-2" />
//                   Add Milestone
//                 </Button>
//               )}
//             </div>
//           </div>
//         </DialogHeader>

//         <div className="flex-1 flex flex-col gap-4 overflow-hidden min-h-0">
//           {/* Task fetchers - invisible components that handle data fetching */}
//           {milestoneIds.map((milestoneId: string) => (
//             <MilestoneTaskFetcher
//               key={milestoneId}
//               milestoneId={milestoneId}
//               onTasksLoaded={handleTasksLoaded}
//             />
//           ))}

//           {/* Forms */}
//           {creatingMilestone && (
//             <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
//               <h3 className="text-lg font-semibold mb-4">
//                 Create New Milestone
//               </h3>
//               <MilestoneForm
//                 projectId={projectId}
//                 onSave={handleMilestoneFormSave}
//                 onCancel={() => setCreatingMilestone(false)}
//               />
//             </div>
//           )}

//           {editingMilestone && (
//             <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
//               <h3 className="text-lg font-semibold mb-4">Edit Milestone</h3>
//               <MilestoneForm
//                 milestone={editingMilestone}
//                 projectId={projectId}
//                 onSave={handleMilestoneFormSave}
//                 onCancel={() => setEditingMilestone(null)}
//               />
//             </div>
//           )}

//           {creatingTask && (
//             <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
//               <h3 className="text-lg font-semibold mb-4">Create New Task</h3>
//               <TaskForm
//                 milestoneId={creatingTask}
//                 onSave={handleTaskFormSave}
//                 onCancel={() => setCreatingTask(null)}
//               />
//             </div>
//           )}

//           {editingTask && (
//             <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
//               <h3 className="text-lg font-semibold mb-4">Edit Task</h3>
//               <TaskForm
//                 task={editingTask.task ?? undefined}
//                 milestoneId={editingTask.milestoneId}
//                 onSave={handleTaskFormSave}
//                 onCancel={() => setEditingTask(null)}
//               />
//             </div>
//           )}

//           {/* Milestones List */}
//           <div className="flex-1 overflow-y-auto">
//             {milestones.length === 0 ? (
//               <div className="text-center py-8">
//                 <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
//                 <h3 className="text-lg font-medium text-gray-900 mb-2">
//                   No milestones found
//                 </h3>
//                 <p className="text-gray-600 mb-4">
//                   Create your first milestone to start managing tasks
//                 </p>
//                 <Button onClick={handleCreateMilestone}>
//                   <Plus className="w-4 h-4 mr-2" />
//                   Create Milestone
//                 </Button>
//               </div>
//             ) : (
//               <div className="space-y-4">
//                 {milestones.map((milestone: Milestone) => {
//                   const tasks = tasksByMilestone[milestone.id] || [];
//                   const isExpanded = expandedMilestones.has(milestone.id);

//                   return (
//                     <MilestoneCard
//                       key={milestone.id}
//                       milestone={milestone}
//                       tasks={tasks}
//                       isExpanded={isExpanded}
//                       onToggleExpansion={() =>
//                         toggleMilestoneExpansion(milestone.id)
//                       }
//                       onEdit={() => handleEditMilestone(milestone)}
//                       onDelete={() => handleDeleteMilestone(milestone)}
//                       onAddTask={() => handleCreateTask(milestone.id)}
//                       onEditTask={(task) => handleEditTask(task, milestone.id)}
//                       onDeleteTask={handleDeleteTask}
//                     />
//                   );
//                 })}
//               </div>
//             )}
//           </div>
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// };

// // Milestone Form Component
// const MilestoneForm: React.FC<{
//   milestone?: Milestone;
//   projectId: string;
//   onSave: () => void;
//   onCancel: () => void;
// }> = ({ milestone, projectId, onSave, onCancel }) => {
//   const [formData, setFormData] = useState({
//     title: milestone?.title || "",
//     description: milestone?.description || "",
//     objective: milestone?.objective || "",
//     type: milestone?.type || "milestone",
//     "start-date": milestone?.["start-date"]
//       ? milestone["start-date"].split("T")[0]
//       : "",
//     "end-date": milestone?.["end-date"]
//       ? milestone["end-date"].split("T")[0]
//       : "",
//   });

//   const createMilestoneMutation = useCreateMilestone();
//   const updateMilestoneMutation = useUpdateMilestone();

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();

//     try {
//       const submitData = {
//         ...formData,
//         "project-id": projectId,
//         "creator-id": "default-creator-id", // TODO: Get from auth context
//         "start-date": formData["start-date"]
//           ? new Date(formData["start-date"]).toISOString()
//           : "",
//         "end-date": formData["end-date"]
//           ? new Date(formData["end-date"]).toISOString()
//           : "",
//       };

//       if (milestone) {
//         await updateMilestoneMutation.mutateAsync({
//           id: milestone.id,
//           data: submitData,
//         });
//         toast.success("Milestone updated successfully");
//       } else {
//         await createMilestoneMutation.mutateAsync(submitData);
//         toast.success("Milestone created successfully");
//       }
//       onSave();
//     } catch (error) {
//       console.error("Error saving milestone:", error);
//       toast.error(`Failed to ${milestone ? "update" : "create"} milestone`);
//     }
//   };

//   return (
//     <form onSubmit={handleSubmit} className="space-y-4">
//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">
//           Title
//         </label>
//         <Input
//           type="text"
//           value={formData.title}
//           onChange={(e) =>
//             setFormData((prev) => ({ ...prev, title: e.target.value }))
//           }
//           required
//           placeholder="Enter milestone title"
//         />
//       </div>

//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">
//           Description
//         </label>
//         <Textarea
//           value={formData.description}
//           onChange={(e) =>
//             setFormData((prev) => ({ ...prev, description: e.target.value }))
//           }
//           placeholder="Enter milestone description"
//           rows={3}
//         />
//       </div>

//       <div className="grid grid-cols-2 gap-4">
//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             Start Date
//           </label>
//           <Input
//             type="date"
//             value={formData["start-date"]}
//             onChange={(e) =>
//               setFormData((prev) => ({ ...prev, "start-date": e.target.value }))
//             }
//           />
//         </div>
//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             End Date
//           </label>
//           <Input
//             type="date"
//             value={formData["end-date"]}
//             onChange={(e) =>
//               setFormData((prev) => ({ ...prev, "end-date": e.target.value }))
//             }
//           />
//         </div>
//       </div>

//       <div className="flex justify-end space-x-2">
//         <Button type="button" variant="outline" onClick={onCancel}>
//           Cancel
//         </Button>
//         <Button type="submit">
//           <Save className="w-4 h-4 mr-2" />
//           {milestone ? "Update" : "Create"}
//         </Button>
//       </div>
//     </form>
//   );
// };

// // Task Form Component
// const TaskForm: React.FC<{
//   task?: Task;
//   milestoneId: string;
//   onSave: () => void;
//   onCancel: () => void;
// }> = ({ task, milestoneId, onSave, onCancel }) => {
//   const [formData, setFormData] = useState({
//     name: task?.title || "",
//     description: task?.description || "",
//     priority: (task?.priority as "Low" | "Medium" | "High") || "Low",
//     "start-date": task?.["start-date"]
//       ? formatDateTimeLocal(task["start-date"])
//       : "",
//     "end-date": task?.["end-date"] ? formatDateTimeLocal(task["end-date"]) : "",
//     note: task?.note || "",
//     "meeting-url": task?.["meeting-url"] || "",
//   });

//   const createTaskMutation = useCreateTask();
//   const updateTaskMutation = useUpdateTask();

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();

//     try {
//       const submitData = {
//         ...formData,
//         "milestone-id": milestoneId,
//         "start-date": formData["start-date"]
//           ? new Date(formData["start-date"]).toISOString()
//           : "",
//         "end-date": formData["end-date"]
//           ? new Date(formData["end-date"]).toISOString()
//           : "",
//         progress: 0,
//         overdue: 0,
//       };

//       if (task) {
//         await updateTaskMutation.mutateAsync({
//           taskId: task.id,
//           taskData: submitData,
//         });
//         toast.success("Task updated successfully");
//       } else {
//         await createTaskMutation.mutateAsync(submitData);
//         toast.success("Task created successfully");
//       }
//       onSave();
//     } catch (error) {
//       console.error("Error saving task:", error);
//       toast.error(`Failed to ${task ? "update" : "create"} task`);
//     }
//   };

//   return (
//     <div className="max-h-[60vh] overflow-y-auto">
//       <form onSubmit={handleSubmit} className="space-y-4 pr-2">
//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             Task Name
//           </label>
//           <Input
//             type="text"
//             value={formData.name}
//             onChange={(e) =>
//               setFormData((prev) => ({ ...prev, name: e.target.value }))
//             }
//             required
//             placeholder="Enter task name"
//           />
//         </div>

//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             Description
//           </label>
//           <Textarea
//             value={formData.description}
//             onChange={(e) =>
//               setFormData((prev) => ({ ...prev, description: e.target.value }))
//             }
//             placeholder="Enter task description"
//             rows={3}
//           />
//         </div>

//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             Priority
//           </label>
//           <Select
//             value={formData.priority}
//             onValueChange={(value: "Low" | "Medium" | "High") =>
//               setFormData((prev) => ({ ...prev, priority: value }))
//             }
//           >
//             <SelectTrigger>
//               <SelectValue placeholder="Select priority" />
//             </SelectTrigger>
//             <SelectContent>
//               <SelectItem value="Low">Low</SelectItem>
//               <SelectItem value="Medium">Medium</SelectItem>
//               <SelectItem value="High">High</SelectItem>
//             </SelectContent>
//           </Select>
//         </div>

//         <div className="grid grid-cols-2 gap-4">
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">
//               Start Date & Time
//             </label>
//             <Input
//               type="datetime-local"
//               value={formData["start-date"]}
//               onChange={(e) =>
//                 setFormData((prev) => ({
//                   ...prev,
//                   "start-date": e.target.value,
//                 }))
//               }
//             />
//           </div>
//           <div>
//             <label className="block text-sm font-medium text-gray-700 mb-1">
//               End Date & Time
//             </label>
//             <Input
//               type="datetime-local"
//               value={formData["end-date"]}
//               onChange={(e) =>
//                 setFormData((prev) => ({ ...prev, "end-date": e.target.value }))
//               }
//             />
//           </div>
//         </div>

//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             Meeting URL
//           </label>
//           <Input
//             type="url"
//             value={formData["meeting-url"]}
//             onChange={(e) =>
//               setFormData((prev) => ({
//                 ...prev,
//                 "meeting-url": e.target.value,
//               }))
//             }
//             placeholder="https://meet.google.com/... or https://zoom.us/j/..."
//           />
//         </div>

//         <div>
//           <label className="block text-sm font-medium text-gray-700 mb-1">
//             Notes
//           </label>
//           <Textarea
//             value={formData.note}
//             onChange={(e) =>
//               setFormData((prev) => ({ ...prev, note: e.target.value }))
//             }
//             placeholder="Additional notes"
//             rows={2}
//           />
//         </div>

//         <div className="flex justify-end space-x-2">
//           <Button type="button" variant="outline" onClick={onCancel}>
//             Cancel
//           </Button>
//           <Button type="submit">
//             <Save className="w-4 h-4 mr-2" />
//             {task ? "Update" : "Create"}
//           </Button>
//         </div>
//       </form>
//     </div>
//   );
// };

// export default MeetingTaskModal;

import React, { useState, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Target, Save } from "lucide-react";
import { useCreateTask, useUpdateTask } from "@/hooks/queries/task";
import { toast } from "sonner";

// Helper function to format date-time for input
const formatDateTimeLocal = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

interface MeetingTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task?: {
    id: string;
    title: string;
    description: string;
    priority: string;
    "start-date": string;
    "end-date": string;
    "meeting-url": string | null;
    note: string;
    status: string;
    "milestone-id": string;
  } | null;
  milestoneId: string;
}

interface Task {
  id: string;
  title: string;
  description: string;
  priority: string;
  "start-date": string;
  "end-date": string;
  "meeting-url": string | null;
  note: string;
  status: string;
  "milestone-id": string;
}

const MeetingTaskModal: React.FC<MeetingTaskModalProps> = ({
  isOpen,
  onClose,
  task,
  milestoneId,
}) => {
  // Reset forms when modal closes
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleTaskFormSave = useCallback(() => {
    handleClose();
  }, [handleClose]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center gap-3">
            <Target className="h-6 w-6 text-blue-600" />
            <div>
              <DialogTitle className="text-xl font-bold">
                {task ? "Update Task" : "Create New Task"}
              </DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                {task ? "Edit task details" : "Create a new meeting task"}
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden min-h-0">
          <TaskForm
            task={task ?? undefined}
            milestoneId={milestoneId}
            onSave={handleTaskFormSave}
            onCancel={handleClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Task Form Component
const TaskForm: React.FC<{
  task?: Task;
  milestoneId: string;
  onSave: () => void;
  onCancel: () => void;
}> = ({ task, milestoneId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: task?.title || "",
    description: task?.description || "",
    priority: (task?.priority as "Low" | "Medium" | "High") || "Low",
    "start-date": task?.["start-date"]
      ? formatDateTimeLocal(task["start-date"])
      : "",
    "end-date": task?.["end-date"] ? formatDateTimeLocal(task["end-date"]) : "",
    note: task?.note || "",
    "meeting-url": task?.["meeting-url"] || "",
  });

  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const submitData = {
        ...formData,
        "milestone-id": milestoneId,
        "start-date": formData["start-date"]
          ? new Date(formData["start-date"]).toISOString()
          : "",
        "end-date": formData["end-date"]
          ? new Date(formData["end-date"]).toISOString()
          : "",
        progress: 0,
        overdue: 0,
      };

      if (task) {
        await updateTaskMutation.mutateAsync({
          taskId: task.id,
          taskData: submitData,
        });
        toast.success("Task updated successfully");
      } else {
        await createTaskMutation.mutateAsync(submitData);
        toast.success("Task created successfully");
      }
      onSave();
    } catch (error) {
      console.error("Error saving task:", error);
      toast.error(`Failed to ${task ? "update" : "create"} task`);
    }
  };

  const handleFormDataChange = useCallback((field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  }, []);

  return (
    <div className="max-h-[60vh] overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-4 pr-2">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Task Name
          </label>
          <Input
            type="text"
            value={formData.name}
            onChange={(e) => handleFormDataChange("name", e.target.value)}
            required
            placeholder="Enter task name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <Textarea
            value={formData.description}
            onChange={(e) =>
              handleFormDataChange("description", e.target.value)
            }
            placeholder="Enter task description"
            rows={3}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Priority
          </label>
          <Select
            value={formData.priority}
            onValueChange={(value: "Low" | "Medium" | "High") =>
              handleFormDataChange("priority", value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Low">Low</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="High">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date & Time
            </label>
            <Input
              type="datetime-local"
              value={formData["start-date"]}
              onChange={(e) =>
                handleFormDataChange("start-date", e.target.value)
              }
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date & Time
            </label>
            <Input
              type="datetime-local"
              value={formData["end-date"]}
              onChange={(e) => handleFormDataChange("end-date", e.target.value)}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Meeting URL
          </label>
          <Input
            type="url"
            value={formData["meeting-url"]}
            onChange={(e) =>
              handleFormDataChange("meeting-url", e.target.value)
            }
            placeholder="https://meet.google.com/... or https://zoom.us/j/..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <Textarea
            value={formData.note}
            onChange={(e) => handleFormDataChange("note", e.target.value)}
            placeholder="Additional notes"
            rows={2}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="w-4 h-4 mr-2" />
            {task ? "Update" : "Create"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default MeetingTaskModal;
